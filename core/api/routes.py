from flask import jsonify, request
from core.api import api_bp
from core.utils.auth_utils import auth_required

from features.org_data import get_organization_data
from features.archetype import get_brand_archetype_analysis
from features.brand_guidelines import get_brand_guidelines
from features.image_scraper import scrape_images, get_image_statistics, validate_url, estimate_scraping_time, get_supported_image_types
from features.product_details import get_product_details, load_product_data, get_all_products
from features.template_creator import create_templates, load_user_journey, get_template_statistics, load_communication_settings
from features.html_email_generator import convert_text_to_html, create_html_email_from_text, load_brand_guidelines_for_organization, get_template_cta_text, process_email_batch_to_html
from features.email_sender import send_test_email, send_email_campaign
from features.popup_text_generator import generate_single_popup_content, generate_popup_content_batch, generate_popup_content_from_prompt
from features.popup_configuration_manager import create_popup_configuration, load_popup_configuration, update_popup_configuration, get_popup_preview, save_popup_background_image, get_brand_colors
from features.whatsapp_sender import send_test_whatsapp_message, send_whatsapp_campaign
from features.whatsapp_template_fetcher import fetch_and_store_templates, get_all_templates, create_product_template_mapping
from features.mail_to_html_converter import convert_text_to_html as mail_convert_text_to_html, load_brand_guidelines_for_organization as mail_load_brand_guidelines, get_template_cta_text as mail_get_template_cta_text
from features.channel_optimizer import optimize_channels_for_campaign, get_optimal_channel_for_user, load_engagement_metrics
from features.performance_predictor import predict_campaign_performance, analyze_campaign_performance, get_template_performance_analysis, train_prediction_models, get_performance_insights


@api_bp.route('/hello', methods=['GET'])
@auth_required
def hello():
    return jsonify({'hello': 'world'})


@api_bp.route('/organization-data', methods=['GET'])
def analyze_organization():
    """
    POST API endpoint to analyze organization data from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "url": "...",
            "domain": "...",
            "about": "...",
            "whatTheyDo": "..."
        },
        "saved": true  // indicates if data was saved to storage
    }
    """
    try:
        org_url = 'https://www.analyticsvidhya.com/'

        org_data = get_organization_data(org_url)

        if org_data.get('Domain') == 'Error':
            return jsonify({
                "success": False,
                "error": "Failed to analyze organization data",
                "details": org_data.get('AboutUs', 'Unknown error occurred'),
                "data": org_data
            }), 500

        transformed_data = {
            "url": org_data.get('url', org_url),
            "domain": org_data.get('Domain', ''),
            "about": org_data.get('AboutUs', ''),
            "whatTheyDo": org_data.get('WhatWeDo', '')
        }

        return jsonify({
            "success": True,
            "data": transformed_data,
            "message": "Organization data analyzed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-archetype-analysis', methods=['POST'])
def analyze_brand_archetype():
    """
    POST API endpoint to analyze brand archetype from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com"
    }

    Returns:
    {
        "success": true,
        "data": {
            "missionStatement": "...",
            "visionStatement": "...",
            "brandArchetype": "...",
            "toneOfVoice": "...",
            "preferredTerminology": "...",
            "avoidedTerminology": "...",
            "archetypes": {
                "Creator": {"score": 75.0, "reasoning": "..."},
                "Sage": {"score": 90.0, "reasoning": "..."},
                ...
            }
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        archetype_analysis = get_brand_archetype_analysis(org_url)

        if archetype_analysis.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand archetype",
                "details": archetype_analysis.get('error', 'Unknown error occurred'),
                "data": archetype_analysis
            }), 500

        # Check for various error conditions in the analysis
        mission = archetype_analysis.get('mission', '')
        primary_archetype = archetype_analysis.get('primary_archetype', '')

        # Check for error messages in mission field
        mission_has_error = any(error_phrase in mission.lower() for error_phrase in [
            'error', 'failed', 'not analyzed'
        ])

        # Check for unknown or empty primary archetype
        archetype_has_error = (primary_archetype == 'Unknown' or
                              primary_archetype == '' or
                              'error' in primary_archetype.lower())

        if mission_has_error or archetype_has_error:
            return jsonify({
                "success": False,
                "error": "Failed to complete brand archetype analysis",
                "details": "Analysis returned incomplete or error results",
                "data": archetype_analysis
            }), 500

        brand_identity = {
            "missionStatement": archetype_analysis.get('mission', ''),
            "visionStatement": archetype_analysis.get('vision', ''),
            "brandArchetype": archetype_analysis.get('primary_archetype', ''),
            "toneOfVoice": archetype_analysis.get('tone_of_voice', ''),
            "preferredTerminology": "",
            "avoidedTerminology": "",
            "archetypes": archetype_analysis.get('archetype_scores', {})
        }

        return jsonify({
            "success": True,
            "data": brand_identity,
            "message": "Brand archetype analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-guidelines', methods=['POST'])
@auth_required
def analyze_brand_guidelines():
    """
    POST API endpoint to analyze brand guidelines from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com"
    }

    Returns:
    {
        "success": true,
        "data": {
            "typography": {
                "themeType": "",
                "styleType": "",
                "fontFamily": "",
                "fontSize": "",
                "fontWeight": "",
                "lineHeight": "",
                "letterSpacing": "",
                "textTransform": ""
            },
            "colorGuideline": {
                "themeType": "",
                "primaryColor": "",
                "secondaryColor": "",
                "accentColor": "",
                "neutralColor": "",
                "backgroundColor": "",
                "textColor": "",
                "gradientCss": "",
                "gradientColors": "",
                "gradientDirection": ""
            },
            "ctaThemeStyle": {
                "themeType": "",
                "ctaType": "",
                "ctaSize": "",
                "buttonStyle": "",
                "ctaRadius": "",
                "ctaColor": "",
                "textColor": ""
            }
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        brand_guidelines = get_brand_guidelines(org_url)

        if brand_guidelines.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand guidelines",
                "details": brand_guidelines.get('error', 'Unknown error occurred'),
                "data": brand_guidelines
            }), 500

        if (brand_guidelines.get('primary_color') == '#default' or
            brand_guidelines.get('primary_color_reasoning') == 'Analysis failed - using default value'):
            return jsonify({
                "success": False,
                "error": "Failed to complete brand guidelines analysis",
                "details": "Analysis returned incomplete or error results",
                "data": brand_guidelines
            }), 500

        typography = {
            "themeType": "",
            "styleType": "",
            "fontFamily": brand_guidelines.get('font', ''),
            "fontSize": brand_guidelines.get('font_size', ''),
            "fontWeight": brand_guidelines.get('font_weight', ''),
            "lineHeight": "",
            "letterSpacing": "",
            "textTransform": ""
        }

        color_guideline = {
            "themeType": "",
            "primaryColor": brand_guidelines.get('primary_color', ''),
            "secondaryColor": brand_guidelines.get('secondary_color', ''),
            "accentColor": brand_guidelines.get('accent_color', ''),
            "neutralColor": brand_guidelines.get('neutral_color', ''),
            "backgroundColor": brand_guidelines.get('background_color', ''),
            "textColor": brand_guidelines.get('text_color', ''),
            "gradientCss": brand_guidelines.get('gradient_colors', ''),
            "gradientColors": brand_guidelines.get('gradient_colors', ''),
            "gradientDirection": brand_guidelines.get('gradient_direction', '')
        }

        cta_theme_style = {
            "themeType": "",
            "ctaType": brand_guidelines.get('cta_type', ''),
            "ctaSize": brand_guidelines.get('cta_size', ''),
            "buttonStyle": brand_guidelines.get('button_style', ''),
            "ctaRadius": brand_guidelines.get('border_radius', ''),
            "ctaColor": "",
            "textColor": brand_guidelines.get('text_color', '')
        }

        transformed_data = {
            "typography": typography,
            "colorGuideline": color_guideline,
            "ctaThemeStyle": cta_theme_style
        }

        return jsonify({
            "success": True,
            "data": transformed_data,
            "message": "Brand guidelines analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/scrape-images', methods=['POST'])
@auth_required
def scrape_website_images():
    """
    POST API endpoint to scrape images from a website URL.

    Expected JSON payload:
    {
        "url": "https://example.com",
        "min_pixels": 100,  // optional, defaults to 100
        "num_images": 10,   // optional, no limit if not specified
        "image_types": ["jpg", "jpeg", "png", "webp"],  // optional, defaults to all supported types
        "usage": "Product Images"  // optional, defaults to "All Products"
    }

    Returns:
    {
        "success": true,
        "data": {
            "total_images": 5,
            "images_data": [
                {
                    "id": "uuid-string",
                    "filename": "scraped_image_1.jpg",
                    "original_filename": "product.jpg",
                    "name": "product.jpg",
                    "upload_date": "2024-07-25T10:30:00",
                    "categories": [],
                    "usage": "Product Images",
                    "notes": "",
                    "width": 800,
                    "height": 600,
                    "thumbnail": "base64-encoded-thumbnail",
                    "source": "web_scraping"
                }
            ],
            "skipped_images": 2,
            "errors": [],
            "url": "https://example.com"
        },
        "message": "Successfully scraped 5 images"
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        url = data.get('url')
        if not url:
            return jsonify({
                "success": False,
                "error": "Missing required field: url"
            }), 400

        if not url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        min_pixels = data.get('min_pixels', 100)
        num_images = data.get('num_images')
        image_types = data.get('image_types')
        usage = data.get('usage', 'All Products')

        if not isinstance(min_pixels, int) or min_pixels < 1:
            return jsonify({
                "success": False,
                "error": "min_pixels must be a positive integer"
            }), 400

        if num_images is not None and (not isinstance(num_images, int) or num_images < 1):
            return jsonify({
                "success": False,
                "error": "num_images must be a positive integer"
            }), 400

        if image_types is not None:
            if not isinstance(image_types, list) or not all(isinstance(t, str) for t in image_types):
                return jsonify({
                    "success": False,
                    "error": "image_types must be a list of strings"
                }), 400

            supported_types = get_supported_image_types()
            invalid_types = [t for t in image_types if t.lower() not in supported_types]
            if invalid_types:
                return jsonify({
                    "success": False,
                    "error": f"Unsupported image types: {invalid_types}. Supported types: {supported_types}"
                }), 400

        if not validate_url(url):
            return jsonify({
                "success": False,
                "error": "URL is not accessible or does not respond"
            }), 400

        scraping_results = scrape_images(
            url=url,
            min_pixels=min_pixels,
            num_images=num_images,
            image_types=image_types,
            usage=usage
        )

        if not scraping_results.get('success', False):
            errors = scraping_results.get('errors', ['Unknown error occurred'])
            return jsonify({
                "success": False,
                "error": "Failed to scrape images",
                "details": errors,
                "data": scraping_results
            }), 500

        total_images = scraping_results.get('total_images', 0)
        message = f"Successfully scraped {total_images} image{'s' if total_images != 1 else ''}"

        return jsonify({
            "success": True,
            "data": scraping_results,
            "message": message
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/image-statistics', methods=['GET'])
@auth_required
def get_scraped_image_statistics():
    """
    GET API endpoint to retrieve statistics about scraped images.

    Query parameters:
    - usage_filter (optional): Filter statistics by usage category

    Returns:
    {
        "success": true,
        "data": {
            "total_images": 25,
            "usage_categories": {
                "Product Images": 15,
                "All Products": 10
            },
            "image_types": {
                "jpg": 12,
                "png": 8,
                "webp": 5
            },
            "size_distribution": {
                "small": 5,    // < 500px
                "medium": 12,  // 500-1000px
                "large": 8     // > 1000px
            },
            "source_distribution": {
                "web_scraping": 20,
                "manual_upload": 5,
                "other": 0
            }
        }
    }
    """
    try:
        usage_filter = request.args.get('usage_filter')

        stats = get_image_statistics(usage_filter)

        if 'error' in stats:
            return jsonify({
                "success": False,
                "error": "Failed to retrieve image statistics",
                "details": stats['error']
            }), 500

        return jsonify({
            "success": True,
            "data": stats,
            "message": "Image statistics retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/scraping-info', methods=['GET'])
@auth_required
def get_scraping_info():
    """
    GET API endpoint to retrieve information about image scraping capabilities.

    Returns:
    {
        "success": true,
        "data": {
            "supported_image_types": ["jpg", "jpeg", "png", "webp", "gif"],
            "default_min_pixels": 100,
            "default_usage": "All Products"
        }
    }
    """
    try:
        return jsonify({
            "success": True,
            "data": {
                "supported_image_types": get_supported_image_types(),
                "default_min_pixels": 100,
                "default_usage": "All Products"
            },
            "message": "Scraping information retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/estimate-scraping', methods=['POST'])
@auth_required
def estimate_image_scraping():
    """
    POST API endpoint to estimate scraping time and image count for a URL.

    Expected JSON payload:
    {
        "url": "https://example.com"
    }

    Returns:
    {
        "success": true,
        "data": {
            "estimated_images": 25,
            "estimated_time_seconds": 50,
            "estimated_time_minutes": 0.8,
            "url": "https://example.com"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        url = data.get('url')
        if not url:
            return jsonify({
                "success": False,
                "error": "Missing required field: url"
            }), 400

        if not url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        estimation = estimate_scraping_time(url)

        if not estimation.get('success', False):
            return jsonify({
                "success": False,
                "error": "Failed to estimate scraping time",
                "details": estimation.get('error', 'Unknown error occurred'),
                "data": estimation
            }), 500

        return jsonify({
            "success": True,
            "data": estimation,
            "message": "Scraping estimation completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/product-details', methods=['POST'])
@auth_required
def analyze_product_details():
    """
    POST API endpoint to analyze product details from URL(s).

    Expected JSON payload:
    {
        "product_urls": "https://example.com/product" or ["url1", "url2"],
        "organization_url": "https://company.com"  // optional
    }

    Returns:
    {
        "success": true,
        "data": {
            "name": "Amazing Product",
            "url": "https://example.com/product",
            "summary": "Detailed product description...",
            "features": ["Feature 1", "Feature 2"],
            "type": "Software"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        product_urls = data.get('product_urls')
        if not product_urls:
            return jsonify({
                "success": False,
                "error": "Missing required field: product_urls"
            }), 400

        if isinstance(product_urls, str):
            if not product_urls.startswith(('http://', 'https://')):
                return jsonify({
                    "success": False,
                    "error": "Invalid URL format. URL must start with http:// or https://"
                }), 400
        elif isinstance(product_urls, list):
            if not product_urls:
                return jsonify({
                    "success": False,
                    "error": "product_urls list cannot be empty"
                }), 400
            for url in product_urls:
                if not isinstance(url, str) or not url.startswith(('http://', 'https://')):
                    return jsonify({
                        "success": False,
                        "error": f"Invalid URL format: {url}. All URLs must start with http:// or https://"
                    }), 400
        else:
            return jsonify({
                "success": False,
                "error": "product_urls must be a string or list of strings"
            }), 400

        organization_url = data.get('organization_url')

        if organization_url and not organization_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid organization_url format. URL must start with http:// or https://"
            }), 400

        product_details = get_product_details(product_urls, organization_url)

        def transform_product(product):
            return {
                "name": product.get('Product_Name', ''),
                "url": product.get('Product_URL', ''),
                "summary": product.get('Product_Summary', ''),
                "features": product.get('Product_Features', []),
                "type": product.get('Type_of_Product', '')
            }

        is_list = isinstance(product_details, list)
        if is_list:
            for product in product_details:
                if product.get('Product_Name') == 'Error':
                    return jsonify({
                        "success": False,
                        "error": "Failed to analyze one or more products",
                        "details": "Some products could not be processed",
                        "data": product_details
                    }), 500
            transformed_data = [transform_product(product) for product in product_details]
        else:
            if product_details.get('Product_Name') == 'Error':
                return jsonify({
                    "success": False,
                    "error": "Failed to analyze product details",
                    "details": product_details.get('Product_Summary', 'Unknown error occurred'),
                    "data": product_details
                }), 500
            transformed_data = transform_product(product_details)

        count = len(product_details) if is_list else 1
        message = f"Successfully analyzed {count} product{'s' if count != 1 else ''}"

        return jsonify({
            "success": True,
            "data": transformed_data,
            "message": message
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/create-templates', methods=['POST'])
@auth_required
def create_marketing_templates():
    """
    POST API endpoint to create marketing templates for product stages.

    Expected JSON payload:
    {
        "product_stages": ["Awareness", "Consideration", "Decision"],
        "stage_details": {
            "Awareness": {
                "description": "Introduce the product to potential customers",
                "current_stage": "Unknown",
                "goal_stage": "Aware"
            }
        },
        "num_templates_per_stage": 2,
        "include_emojis": {
            "use_emojis_subject": true,
            "use_emojis_body": false
        },
        "product_data": {
            "Product_Name": "Amazing Product",
            "Company_Name": "Tech Corp",
            "Product_Features": ["Feature 1", "Feature 2"],
            "Product_Summary": "Product description...",
            "Product_URL": "https://example.com/product"
        },
        "organization_url": "https://company.com"  // optional
    }

    Returns:
    {
        "success": true,
        "data": {
            "total_templates": 6,
            "stage_results": {
                "Awareness": {
                    "success": true,
                    "templates_generated": 2,
                    "message": "Templates generated successfully",
                    "templates": [...]
                }
            },
            "errors": []
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        product_stages = data.get('product_stages')
        if not product_stages:
            return jsonify({
                "success": False,
                "error": "Missing required field: product_stages"
            }), 400

        if not isinstance(product_stages, list) or not product_stages:
            return jsonify({
                "success": False,
                "error": "product_stages must be a non-empty list"
            }), 400

        stage_details = data.get('stage_details')
        if not stage_details:
            return jsonify({
                "success": False,
                "error": "Missing required field: stage_details"
            }), 400

        if not isinstance(stage_details, dict):
            return jsonify({
                "success": False,
                "error": "stage_details must be a dictionary"
            }), 400

        num_templates_per_stage = data.get('num_templates_per_stage')
        if not num_templates_per_stage:
            return jsonify({
                "success": False,
                "error": "Missing required field: num_templates_per_stage"
            }), 400

        if not isinstance(num_templates_per_stage, int) or num_templates_per_stage < 1:
            return jsonify({
                "success": False,
                "error": "num_templates_per_stage must be a positive integer"
            }), 400

        include_emojis = data.get('include_emojis')
        if not include_emojis:
            return jsonify({
                "success": False,
                "error": "Missing required field: include_emojis"
            }), 400

        if not isinstance(include_emojis, dict):
            return jsonify({
                "success": False,
                "error": "include_emojis must be a dictionary"
            }), 400

        product_data = data.get('product_data')
        if not product_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: product_data"
            }), 400

        if not isinstance(product_data, dict):
            return jsonify({
                "success": False,
                "error": "product_data must be a dictionary"
            }), 400

        missing_stages = [stage for stage in product_stages if stage not in stage_details]
        if missing_stages:
            return jsonify({
                "success": False,
                "error": f"Missing stage details for: {missing_stages}"
            }), 400

        organization_url = data.get('organization_url')

        if organization_url and not organization_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid organization_url format. URL must start with http:// or https://"
            }), 400

        required_emoji_keys = ['use_emojis_subject', 'use_emojis_body']
        for key in required_emoji_keys:
            if key not in include_emojis:
                return jsonify({
                    "success": False,
                    "error": f"Missing emoji setting: {key}"
                }), 400
            if not isinstance(include_emojis[key], bool):
                return jsonify({
                    "success": False,
                    "error": f"Emoji setting {key} must be a boolean"
                }), 400

        template_results = create_templates(
            product_stages=product_stages,
            stage_details=stage_details,
            num_templates_per_stage=num_templates_per_stage,
            include_emojis=include_emojis,
            product_data=product_data,
            organization_url=organization_url
        )

        if not template_results.get('success', False):
            errors = template_results.get('errors', ['Unknown error occurred'])
            return jsonify({
                "success": False,
                "error": "Failed to create templates",
                "details": errors,
                "data": template_results
            }), 500

        total_templates = template_results.get('total_templates', 0)
        message = f"Successfully created {total_templates} template{'s' if total_templates != 1 else ''}"

        return jsonify({
            "success": True,
            "data": template_results,
            "message": message
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/products', methods=['GET'])
@auth_required
def get_products():
    """
    GET API endpoint to retrieve stored product data.

    Query parameters:
    - organization_url (optional): Filter by organization URL
    - product_name (optional): Get specific product by name

    Returns:
    {
        "success": true,
        "data": [...] or {...},
        "count": 5
    }
    """
    try:
        organization_url = request.args.get('organization_url')
        product_name = request.args.get('product_name')

        if product_name:
            products = load_product_data(product_name=product_name)
            count = 1 if products else 0
        elif organization_url:
            products = get_all_products(organization_url=organization_url, filter_by_org=True)
            count = len(products) if isinstance(products, list) else 0
        else:
            products = get_all_products()
            count = len(products) if isinstance(products, list) else 0

        return jsonify({
            "success": True,
            "data": products,
            "count": count,
            "message": f"Retrieved {count} product{'s' if count != 1 else ''}"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/template-statistics', methods=['GET'])
@auth_required
def get_template_stats():
    """
    GET API endpoint to retrieve template statistics.

    Query parameters:
    - organization_url (optional): Filter by organization URL

    Returns:
    {
        "success": true,
        "data": {
            "total_templates": 15,
            "stages": {
                "Awareness": 5,
                "Consideration": 5,
                "Decision": 5
            },
            "emoji_usage": {
                "with_emojis": 8,
                "without_emojis": 7
            },
            "channels": {
                "Email": 15
            }
        }
    }
    """
    try:
        organization_url = request.args.get('organization_url')

        stats = get_template_statistics(organization_url)

        return jsonify({
            "success": True,
            "data": stats,
            "message": "Template statistics retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/user-journey', methods=['GET'])
@auth_required
def get_user_journey():
    """
    GET API endpoint to retrieve user journey stages.

    Query parameters:
    - product_name (optional): Get journey for specific product

    Returns:
    {
        "success": true,
        "data": [
            {
                "stage": "Awareness",
                "description": "Customer becomes aware of the product",
                "goal": "Generate interest and awareness"
            }
        ]
    }
    """
    try:
        product_name = request.args.get('product_name')

        journey = load_user_journey(product_name)

        return jsonify({
            "success": True,
            "data": journey,
            "message": "User journey retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/convert-text-to-html', methods=['POST'])
@auth_required
def convert_email_text_to_html():
    """
    Returns:
    {
        "success": true,
        "data": {
            "html_content": "HTML formatted email content"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        email_content = data.get('email_content')
        if not email_content:
            return jsonify({
                "success": False,
                "error": "Missing required field: email_content"
            }), 400

        if not isinstance(email_content, dict):
            return jsonify({
                "success": False,
                "error": "email_content must be a dictionary"
            }), 400

        product_url = data.get('product_url')
        product_name = data.get('product_name')
        communication_settings = data.get('communication_settings')
        recipient_email = data.get('recipient_email')
        recipient_first_name = data.get('recipient_first_name')
        brand_guidelines = data.get('brand_guidelines')
        template_name = data.get('template_name')

        html_content = convert_text_to_html(
            email_content=email_content,
            product_url=product_url,
            product_name=product_name,
            communication_settings=communication_settings,
            recipient_email=recipient_email,
            recipient_first_name=recipient_first_name,
            brand_guidelines=brand_guidelines,
            template_name=template_name
        )

        return jsonify({
            "success": True,
            "data": {
                "html_content": html_content
            },
            "message": "Email converted to HTML successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/create-html-email', methods=['POST'])
@auth_required
def create_html_email():
    """
    Returns:
    {
        "success": true,
        "data": {
            "html_email": "Complete HTML email"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        text_content = data.get('text_content')
        if not text_content:
            return jsonify({
                "success": False,
                "error": "Missing required field: text_content"
            }), 400

        subject = data.get('subject', 'Email Subject')
        product_url = data.get('product_url')
        product_name = data.get('product_name')
        sender_name = data.get('sender_name', 'OpenEngage Team')
        recipient_email = data.get('recipient_email')
        recipient_first_name = data.get('recipient_first_name')
        organization_url = data.get('organization_url')
        template_name = data.get('template_name')
        utm_params = data.get('utm_params')

        html_email = create_html_email_from_text(
            text_content=text_content,
            subject=subject,
            product_url=product_url,
            product_name=product_name,
            sender_name=sender_name,
            recipient_email=recipient_email,
            recipient_first_name=recipient_first_name,
            organization_url=organization_url,
            template_name=template_name,
            utm_params=utm_params
        )

        return jsonify({
            "success": True,
            "data": {
                "html_email": html_email
            },
            "message": "HTML email created successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/send-test-email', methods=['POST'])
@auth_required
def send_test_email_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "results": {...},
            "provider": "SparkPost"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        recipient_email = data.get('recipient_email')
        if not recipient_email:
            return jsonify({
                "success": False,
                "error": "Missing required field: recipient_email"
            }), 400

        subject = data.get('subject')
        if not subject:
            return jsonify({
                "success": False,
                "error": "Missing required field: subject"
            }), 400

        html_content = data.get('html_content')
        if not html_content:
            return jsonify({
                "success": False,
                "error": "Missing required field: html_content"
            }), 400

        provider = data.get('provider', 'SparkPost')

        result = send_test_email(
            recipient_email=recipient_email,
            subject=subject,
            html_content=html_content,
            provider=provider
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": "Test email sent successfully" if result.get('success') else "Failed to send test email"
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/send-email-campaign', methods=['POST'])
@auth_required
def send_email_campaign_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "campaign_results": {...},
            "provider": "SparkPost",
            "total_emails": 100
        }
    }
    """
    try:
        import pandas as pd

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        email_data = data.get('email_data')
        if not email_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: email_data"
            }), 400

        if not isinstance(email_data, list):
            return jsonify({
                "success": False,
                "error": "email_data must be a list of email records"
            }), 400

        provider = data.get('provider')

        email_df = pd.DataFrame(email_data)

        result = send_email_campaign(
            email_data=email_df,
            provider=provider
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": "Email campaign sent successfully" if result.get('success') else "Failed to send email campaign"
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/generate-popup-content', methods=['POST'])
@auth_required
def generate_popup_content_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "popup_content": {...}
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        first_name = data.get('first_name')
        if not first_name:
            return jsonify({
                "success": False,
                "error": "Missing required field: first_name"
            }), 400

        user_behavior = data.get('user_behavior')
        if not user_behavior:
            return jsonify({
                "success": False,
                "error": "Missing required field: user_behavior"
            }), 400

        target_product = data.get('target_product')
        if not target_product:
            return jsonify({
                "success": False,
                "error": "Missing required field: target_product"
            }), 400

        user_stage = data.get('user_stage')
        if not user_stage:
            return jsonify({
                "success": False,
                "error": "Missing required field: user_stage"
            }), 400

        api_key = data.get('api_key')

        result = generate_single_popup_content(
            first_name=first_name,
            user_behavior=user_behavior,
            target_product=target_product,
            user_stage=user_stage,
            api_key=api_key
        )

        return jsonify({
            "success": True,
            "data": {
                "popup_content": result
            },
            "message": "Popup content generated successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/generate-popup-batch', methods=['POST'])
@auth_required
def generate_popup_batch_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "generated_content": [...],
            "total_processed": 100
        }
    }
    """
    try:
        import pandas as pd

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        user_data = data.get('user_data')
        if not user_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: user_data"
            }), 400

        if not isinstance(user_data, list):
            return jsonify({
                "success": False,
                "error": "user_data must be a list of user records"
            }), 400

        max_workers = data.get('max_workers', 5)
        batch_size = data.get('batch_size', 50)
        api_key = data.get('api_key')

        user_df = pd.DataFrame(user_data)

        result_df = generate_popup_content_batch(
            data_df=user_df,
            max_workers=max_workers,
            batch_size=batch_size,
            api_key=api_key
        )

        result_data = result_df.to_dict('records') if not result_df.empty else []

        return jsonify({
            "success": True,
            "data": {
                "generated_content": result_data,
                "total_processed": len(result_data)
            },
            "message": f"Popup content generated for {len(result_data)} users"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/create-popup-config', methods=['POST'])
@auth_required
def create_popup_config_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "config_file": "popupdata.json",
            "message": "Popup configuration saved successfully"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        config_data = data.get('config_data')
        if not config_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: config_data"
            }), 400

        if not isinstance(config_data, dict):
            return jsonify({
                "success": False,
                "error": "config_data must be a dictionary"
            }), 400

        config_file = data.get('config_file', 'popupdata.json')

        result = create_popup_configuration(
            config_data=config_data,
            config_file=config_file
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": result.get('message', 'Popup configuration processed')
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/load-popup-config', methods=['POST'])
@auth_required
def load_popup_config_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "configuration": {...},
            "config_file": "popupdata.json"
        }
    }
    """
    try:
        data = request.get_json()
        config_file = 'popupdata.json'

        if data:
            config_file = data.get('config_file', 'popupdata.json')

        result = load_popup_configuration(config_file=config_file)

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": "Popup configuration loaded successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/update-popup-config', methods=['POST'])
@auth_required
def update_popup_config_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "updated_fields": [...],
            "message": "Popup configuration updated successfully"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        updates = data.get('updates')
        if not updates:
            return jsonify({
                "success": False,
                "error": "Missing required field: updates"
            }), 400

        if not isinstance(updates, dict):
            return jsonify({
                "success": False,
                "error": "updates must be a dictionary"
            }), 400

        config_file = data.get('config_file', 'popupdata.json')

        result = update_popup_configuration(
            updates=updates,
            config_file=config_file
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": result.get('message', 'Popup configuration update processed')
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/send-test-whatsapp', methods=['POST'])
@auth_required
def send_test_whatsapp_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "message_id": "...",
            "status": "sent"
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        recipient_phone = data.get('recipient_phone')
        if not recipient_phone:
            return jsonify({
                "success": False,
                "error": "Missing required field: recipient_phone"
            }), 400

        selected_template_id = data.get('selected_template_id')
        if not selected_template_id:
            return jsonify({
                "success": False,
                "error": "Missing required field: selected_template_id"
            }), 400

        variable_values = data.get('variable_values')
        if not variable_values:
            return jsonify({
                "success": False,
                "error": "Missing required field: variable_values"
            }), 400

        if not isinstance(variable_values, list):
            return jsonify({
                "success": False,
                "error": "variable_values must be a list"
            }), 400

        selected_esp = data.get('selected_esp', 'Gupshup')

        result = send_test_whatsapp_message(
            recipient_phone=recipient_phone,
            selected_template_id=selected_template_id,
            variable_values=variable_values,
            selected_esp=selected_esp
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": "Test WhatsApp message sent successfully" if result.get('success') else "Failed to send test WhatsApp message"
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/send-whatsapp-campaign', methods=['POST'])
@auth_required
def send_whatsapp_campaign_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "results": {...},
            "campaign_data": [...]
        }
    }
    """
    try:
        import pandas as pd

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        campaign_data = data.get('campaign_data')
        if not campaign_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: campaign_data"
            }), 400

        if not isinstance(campaign_data, list):
            return jsonify({
                "success": False,
                "error": "campaign_data must be a list of campaign records"
            }), 400

        provider = data.get('provider', 'Gupshup')

        campaign_df = pd.DataFrame(campaign_data)

        result = send_whatsapp_campaign(
            campaign_data=campaign_df,
            provider=provider
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": "WhatsApp campaign sent successfully" if result.get('success') else "Failed to send WhatsApp campaign"
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/fetch-whatsapp-templates', methods=['POST'])
@auth_required
def fetch_whatsapp_templates_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "fetched_count": 10,
            "stored_count": 10,
            "templates": [...]
        }
    }
    """
    try:
        data = request.get_json()
        api_key = None
        app_id = None

        if data:
            api_key = data.get('api_key')
            app_id = data.get('app_id')

        result = fetch_and_store_templates(
            api_key=api_key,
            app_id=app_id
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": "WhatsApp templates fetched and stored successfully" if result.get('success') else "Failed to fetch WhatsApp templates"
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/get-whatsapp-templates', methods=['POST'])
@auth_required
def get_whatsapp_templates_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "templates": {...},
            "product_mappings": {...},
            "count": 10
        }
    }
    """
    try:
        result = get_all_templates()

        return jsonify({
            "success": True,
            "data": result,
            "message": "WhatsApp templates retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/create-template-mapping', methods=['POST'])
@auth_required
def create_template_mapping_api():
    """
    Returns:
    {
        "success": true,
        "data": {
            "product_name": "...",
            "template_id": "...",
            "message": "Successfully mapped..."
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        product_name = data.get('product_name')
        if not product_name:
            return jsonify({
                "success": False,
                "error": "Missing required field: product_name"
            }), 400

        template_id = data.get('template_id')
        if not template_id:
            return jsonify({
                "success": False,
                "error": "Missing required field: template_id"
            }), 400

        result = create_product_template_mapping(
            product_name=product_name,
            template_id=template_id
        )

        return jsonify({
            "success": result.get('success', False),
            "data": result,
            "message": result.get('message', 'Template mapping processed')
        }), 200 if result.get('success') else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/mail-convert-text-to-html', methods=['POST'])
@auth_required
def mail_convert_text_to_html_api():
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        email_content = data.get('email_content')
        if not email_content:
            return jsonify({
                "success": False,
                "error": "Missing required field: email_content"
            }), 400

        if not isinstance(email_content, dict):
            return jsonify({
                "success": False,
                "error": "email_content must be a dictionary"
            }), 400

        product_url = data.get('product_url')
        product_name = data.get('product_name')
        communication_settings = data.get('communication_settings')
        recipient_email = data.get('recipient_email')
        recipient_first_name = data.get('recipient_first_name')
        brand_guidelines = data.get('brand_guidelines')
        template_name = data.get('template_name')
        organization_url = data.get('organization_url')

        html_content = mail_convert_text_to_html(
            email_content=email_content,
            product_url=product_url,
            product_name=product_name,
            communication_settings=communication_settings,
            recipient_email=recipient_email,
            recipient_first_name=recipient_first_name,
            brand_guidelines=brand_guidelines,
            template_name=template_name,
            organization_url=organization_url
        )

        return jsonify({
            "success": True,
            "data": {
                "html_content": html_content
            },
            "message": "Email converted to HTML successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/mail-load-brand-guidelines', methods=['POST'])
@auth_required
def mail_load_brand_guidelines_api():
    try:
        data = request.get_json()
        organization_url = None

        if data:
            organization_url = data.get('organization_url')

        brand_guidelines = mail_load_brand_guidelines(organization_url)

        return jsonify({
            "success": True,
            "data": {
                "brand_guidelines": brand_guidelines
            },
            "message": "Brand guidelines loaded successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/mail-get-template-cta-text', methods=['POST'])
@auth_required
def mail_get_template_cta_text_api():
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        template_name = data.get('template_name')
        if not template_name:
            return jsonify({
                "success": False,
                "error": "Missing required field: template_name"
            }), 400

        default_cta = data.get('default_cta', 'Learn More')

        cta_text = mail_get_template_cta_text(template_name, default_cta)

        return jsonify({
            "success": True,
            "data": {
                "cta_text": cta_text,
                "template_name": template_name
            },
            "message": "CTA text retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/optimize-channels-for-campaign', methods=['POST'])
@auth_required
def optimize_channels_for_campaign_api():
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        users_data_path = data.get('users_data_path')
        if not users_data_path:
            return jsonify({
                "success": False,
                "error": "Missing required field: users_data_path"
            }), 400

        content_type = data.get('content_type', 'general')
        urgency_level = data.get('urgency_level', 'medium')
        performance_data_path = data.get('performance_data_path')
        output_dir = data.get('output_dir')
        weights = data.get('weights')

        result = optimize_channels_for_campaign(
            users_data_path=users_data_path,
            content_type=content_type,
            urgency_level=urgency_level,
            performance_data_path=performance_data_path,
            output_dir=output_dir,
            weights=weights
        )

        if isinstance(result, dict) and result:
            segmented_data = {}
            for channel, df in result.items():
                if hasattr(df, 'to_dict'):
                    segmented_data[channel] = df.to_dict('records')
                else:
                    segmented_data[channel] = df

            return jsonify({
                "success": True,
                "data": {
                    "segmented_users": segmented_data,
                    "total_channels": len(segmented_data)
                },
                "message": "Channel optimization completed successfully"
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to optimize channels",
                "details": "No valid segmentation results returned"
            }), 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/get-optimal-channel-for-user', methods=['POST'])
@auth_required
def get_optimal_channel_for_user_api():
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        user_data = data.get('user_data')
        if not user_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: user_data"
            }), 400

        if not isinstance(user_data, dict):
            return jsonify({
                "success": False,
                "error": "user_data must be a dictionary"
            }), 400

        content_type = data.get('content_type', 'general')
        urgency_level = data.get('urgency_level', 'medium')
        performance_data_path = data.get('performance_data_path')
        weights = data.get('weights')

        result = get_optimal_channel_for_user(
            user_data=user_data,
            content_type=content_type,
            urgency_level=urgency_level,
            performance_data_path=performance_data_path,
            weights=weights
        )

        return jsonify({
            "success": True,
            "data": result,
            "message": "Optimal channel recommendation generated successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/load-engagement-metrics', methods=['POST'])
@auth_required
def load_engagement_metrics_api():
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        performance_data_path = data.get('performance_data_path')
        if not performance_data_path:
            return jsonify({
                "success": False,
                "error": "Missing required field: performance_data_path"
            }), 400

        engagement_metrics = load_engagement_metrics(performance_data_path)

        return jsonify({
            "success": True,
            "data": {
                "engagement_metrics": engagement_metrics,
                "total_users": len(engagement_metrics)
            },
            "message": "Engagement metrics loaded successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/predict-campaign-performance', methods=['POST'])
@auth_required
def predict_campaign_performance_api():
    try:
        data = request.get_json()

        campaign_file = None
        performance_file = None

        if data:
            campaign_file = data.get('campaign_file')
            performance_file = data.get('performance_file')

        result = predict_campaign_performance(
            campaign_file=campaign_file,
            performance_file=performance_file
        )

        return jsonify({
            "success": True,
            "data": result,
            "message": "Campaign performance prediction completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/analyze-campaign-performance', methods=['POST'])
@auth_required
def analyze_campaign_performance_api():
    try:
        data = request.get_json()

        performance_file = None

        if data:
            performance_file = data.get('performance_file')

        result = analyze_campaign_performance(performance_file=performance_file)

        return jsonify({
            "success": True,
            "data": result,
            "message": "Campaign performance analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/get-template-performance-analysis', methods=['POST'])
@auth_required
def get_template_performance_analysis_api():
    try:
        import pandas as pd

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        performance_data = data.get('performance_data')
        if not performance_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: performance_data"
            }), 400

        if not isinstance(performance_data, list):
            return jsonify({
                "success": False,
                "error": "performance_data must be a list of records"
            }), 400

        performance_df = pd.DataFrame(performance_data)

        result_df = get_template_performance_analysis(performance_df)

        result_data = result_df.to_dict('records') if not result_df.empty else []

        return jsonify({
            "success": True,
            "data": {
                "template_analysis": result_data,
                "total_templates": len(result_data)
            },
            "message": "Template performance analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/train-prediction-models', methods=['POST'])
@auth_required
def train_prediction_models_api():
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        performance_file = data.get('performance_file')
        if not performance_file:
            return jsonify({
                "success": False,
                "error": "Missing required field: performance_file"
            }), 400

        success = train_prediction_models(performance_file)

        return jsonify({
            "success": success,
            "data": {
                "training_completed": success,
                "performance_file": performance_file
            },
            "message": "Model training completed successfully" if success else "Model training failed"
        }), 200 if success else 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/get-performance-insights', methods=['POST'])
@auth_required
def get_performance_insights_api():
    try:
        data = request.get_json()

        performance_file = None
        campaign_file = None

        if data:
            performance_file = data.get('performance_file')
            campaign_file = data.get('campaign_file')

        result = get_performance_insights(
            performance_file=performance_file,
            campaign_file=campaign_file
        )

        return jsonify({
            "success": True,
            "data": result,
            "message": "Performance insights generated successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500
